# Other AI accelerators

vLLM is a Python library that supports the following AI accelerators. Select your AI accelerator type to see vendor specific instructions:

:::::{tab-set}
:sync-group: device

::::{tab-item} Google TPU
:selected:
:sync: tpu

:::{include} ai_accelerator/tpu.inc.md
:start-after: "# Installation"
:end-before: "## Requirements"
:::

::::

::::{tab-item} Intel Gaudi
:sync: hpu-gaudi

:::{include} ai_accelerator/hpu-gaudi.inc.md
:start-after: "# Installation"
:end-before: "## Requirements"
:::

::::

::::{tab-item} AWS Neuron
:sync: neuron

:::{include} ai_accelerator/neuron.inc.md
:start-after: "# Installation"
:end-before: "## Requirements"
:::

::::

:::::

## Requirements

:::::{tab-set}
:sync-group: device

::::{tab-item} Google TPU
:sync: tpu

:::{include} ai_accelerator/tpu.inc.md
:start-after: "## Requirements"
:end-before: "## Configure a new environment"
:::

::::

::::{tab-item} Intel Gaudi
:sync: hpu-gaudi

:::{include} ai_accelerator/hpu-gaudi.inc.md
:start-after: "## Requirements"
:end-before: "## Configure a new environment"
:::

::::

::::{tab-item} AWS Neuron
:sync: neuron

:::{include} ai_accelerator/neuron.inc.md
:start-after: "## Requirements"
:end-before: "## Configure a new environment"
:::

::::

:::::

## Configure a new environment

:::::{tab-set}
:sync-group: device

::::{tab-item} Google TPU
:sync: tpu

:::{include} ai_accelerator/tpu.inc.md
:start-after: "## Configure a new environment"
:end-before: "## Set up using Python"
:::

::::

::::{tab-item} Intel Gaudi
:sync: hpu-gaudi

:::{include} ai_accelerator/hpu-gaudi.inc.md
:start-after: "## Configure a new environment"
:end-before: "## Set up using Python"
:::

::::

::::{tab-item} AWS Neuron
:sync: neuron

:::{include} ai_accelerator/neuron.inc.md
:start-after: "## Configure a new environment"
:end-before: "## Set up using Python"
:::

::::

:::::

## Set up using Python

### Pre-built wheels

:::::{tab-set}
:sync-group: device

::::{tab-item} Google TPU
:sync: tpu

:::{include} ai_accelerator/tpu.inc.md
:start-after: "### Pre-built wheels"
:end-before: "### Build wheel from source"
:::

::::

::::{tab-item} Intel Gaudi
:sync: hpu-gaudi

:::{include} ai_accelerator/hpu-gaudi.inc.md
:start-after: "### Pre-built wheels"
:end-before: "### Build wheel from source"
:::

::::

::::{tab-item} AWS Neuron
:sync: neuron

:::{include} ai_accelerator/neuron.inc.md
:start-after: "### Pre-built wheels"
:end-before: "### Build wheel from source"
:::

::::

:::::

### Build wheel from source

:::::{tab-set}
:sync-group: device

::::{tab-item} Google TPU
:sync: tpu

:::{include} ai_accelerator/tpu.inc.md
:start-after: "### Build wheel from source"
:end-before: "## Set up using Docker"
:::

::::

::::{tab-item} Intel Gaudi
:sync: hpu-gaudi

:::{include} ai_accelerator/hpu-gaudi.inc.md
:start-after: "### Build wheel from source"
:end-before: "## Set up using Docker"
:::

::::

::::{tab-item} AWS Neuron
:sync: neuron

:::{include} ai_accelerator/neuron.inc.md
:start-after: "### Build wheel from source"
:end-before: "## Set up using Docker"
:::

::::

:::::

## Set up using Docker

### Pre-built images

:::::{tab-set}
:sync-group: device

::::{tab-item} Google TPU
:sync: tpu

:::{include} ai_accelerator/tpu.inc.md
:start-after: "### Pre-built images"
:end-before: "### Build image from source"
:::

::::

::::{tab-item} Intel Gaudi
:sync: hpu-gaudi

:::{include} ai_accelerator/hpu-gaudi.inc.md
:start-after: "### Pre-built images"
:end-before: "### Build image from source"
:::

::::

::::{tab-item} AWS Neuron
:sync: neuron

:::{include} ai_accelerator/neuron.inc.md
:start-after: "### Pre-built images"
:end-before: "### Build image from source"
:::

::::

:::::

### Build image from source

:::::{tab-set}
:sync-group: device

::::{tab-item} Google TPU
:sync: tpu

:::{include} ai_accelerator/tpu.inc.md
:start-after: "### Build image from source"
:end-before: "## Extra information"
:::

::::

::::{tab-item} Intel Gaudi
:sync: hpu-gaudi

:::{include} ai_accelerator/hpu-gaudi.inc.md
:start-after: "### Build image from source"
:end-before: "## Extra information"
:::

::::

::::{tab-item} AWS Neuron
:sync: neuron

:::{include} ai_accelerator/neuron.inc.md
:start-after: "### Build image from source"
:end-before: "## Extra information"
:::

::::

:::::

## Extra information

:::::{tab-set}
:sync-group: device

::::{tab-item} Google TPU
:sync: tpu

:::{include} ai_accelerator/tpu.inc.md
:start-after: "## Extra information"
:::

::::

::::{tab-item} Intel Gaudi
:sync: hpu-gaudi

:::{include} ai_accelerator/hpu-gaudi.inc.md
:start-after: "## Extra information"
:::

::::

::::{tab-item} AWS Neuron
:sync: neuron

:::{include} ai_accelerator/neuron.inc.md
:start-after: "## Extra information"
:::

::::

:::::

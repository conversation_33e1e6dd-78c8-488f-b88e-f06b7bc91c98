#!/usr/bin/env python3
"""
测试脚本：验证图类型修改是否正确
测试移除 long 状态后，只保留 short 和 original 两种状态的逻辑
"""

import sys
import os

# 添加 vllm 路径
sys.path.insert(0, '/home/<USER>')

def test_graph_type_logic():
    """测试图类型选择逻辑"""

    # 模拟 ModelRunner 的图类型选择方法
    def _get_graph_type_for_execution(batch_size: int, max_decode_seq_len: int, max_seq_len_to_capture: int) -> str:
        """
        根据批处理大小和序列长度确定图类型
        """
        # If max_seq_len_to_capture <= 8192, always use original (no need for short graph)
        if max_seq_len_to_capture <= 8192:
            return "original"

        if batch_size > 64 or max_decode_seq_len >= 8192:
            return "original"
        else:
            return "short"

    # 测试用例 - 当 max_seq_len_to_capture > 8192 时
    print("测试场景 1: max_seq_len_to_capture = 32768 (> 8192)")
    test_cases_large_context = [
        # (batch_size, max_decode_seq_len, max_seq_len_to_capture, expected_graph_type)
        (32, 4096, 32768, "short"),      # BS ≤ 64 且 SeqLen < 8192 → short
        (64, 7999, 32768, "short"),      # BS ≤ 64 且 SeqLen < 8192 → short
        (64, 8192, 32768, "original"),   # BS ≤ 64 但 SeqLen = 8192 → original (边界值)
        (64, 8193, 32768, "original"),   # BS ≤ 64 但 SeqLen > 8192 → original
        (65, 4096, 32768, "original"),   # BS > 64 → original (无论 SeqLen)
        (128, 16384, 32768, "original"), # BS > 64 → original
        (1, 8192, 32768, "original"),    # BS ≤ 64 但 SeqLen = 8192 → original
        (1, 8191, 32768, "short"),       # BS ≤ 64 且 SeqLen < 8192 → short
    ]

    # 测试用例 - 当 max_seq_len_to_capture <= 8192 时
    print("测试场景 2: max_seq_len_to_capture = 4096 (≤ 8192)")
    test_cases_small_context = [
        # (batch_size, max_decode_seq_len, max_seq_len_to_capture, expected_graph_type)
        (32, 2048, 4096, "original"),    # 所有情况都应该是 original
        (64, 3999, 4096, "original"),    # 所有情况都应该是 original
        (65, 2048, 4096, "original"),    # 所有情况都应该是 original
        (128, 4096, 4096, "original"),   # 所有情况都应该是 original
        (1, 4096, 4096, "original"),     # 所有情况都应该是 original
    ]

    print("=" * 80)

    all_passed = True

    # 测试大上下文场景
    for batch_size, max_decode_seq_len, max_seq_len_to_capture, expected in test_cases_large_context:
        result = _get_graph_type_for_execution(batch_size, max_decode_seq_len, max_seq_len_to_capture)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        print(f"BS={batch_size:3d}, SeqLen={max_decode_seq_len:5d}, MaxSeq={max_seq_len_to_capture:5d} → {result:8s} (expected: {expected:8s}) {status}")

        if result != expected:
            all_passed = False

    print("\n" + "=" * 80)

    # 测试小上下文场景
    for batch_size, max_decode_seq_len, max_seq_len_to_capture, expected in test_cases_small_context:
        result = _get_graph_type_for_execution(batch_size, max_decode_seq_len, max_seq_len_to_capture)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        print(f"BS={batch_size:3d}, SeqLen={max_decode_seq_len:5d}, MaxSeq={max_seq_len_to_capture:5d} → {result:8s} (expected: {expected:8s}) {status}")

        if result != expected:
            all_passed = False

    print("=" * 80)
    if all_passed:
        print("✅ 所有测试用例通过！图类型逻辑修改正确。")
    else:
        print("❌ 部分测试用例失败！需要检查逻辑。")

    return all_passed

def test_graph_capture_logic():
    """测试图捕获逻辑"""

    def _get_graphs_to_capture_for_batch_size(batch_size: int,
                                            short_seq_len_to_capture: int = 8192,
                                            max_seq_len_to_capture: int = 32768) -> list:
        """
        确定给定批处理大小需要捕获哪些图
        """
        # If max_seq_len_to_capture <= 8192, only capture original graph for all batch sizes
        if max_seq_len_to_capture <= 8192:
            return [("original", max_seq_len_to_capture)]

        if batch_size > 64:
            # BS > 64: 只捕获 original 图
            return [("original", max_seq_len_to_capture)]
        else:
            # BS ≤ 64 and max_seq_len > 8192: 捕获 short 和 original 图
            graphs = [("short", short_seq_len_to_capture)]
            graphs.append(("original", max_seq_len_to_capture))
            return graphs

    print("\n测试图捕获逻辑:")
    print("=" * 80)

    # 测试大上下文场景 (max_seq_len_to_capture = 32768)
    print("场景 1: max_seq_len_to_capture = 32768 (> 8192)")
    test_cases_large = [
        # (batch_size, max_seq_len_to_capture, expected_graph_types)
        (32, 32768, ["short", "original"]),   # BS ≤ 64 → 捕获 short 和 original
        (64, 32768, ["short", "original"]),   # BS ≤ 64 → 捕获 short 和 original
        (65, 32768, ["original"]),            # BS > 64 → 只捕获 original
        (128, 32768, ["original"]),           # BS > 64 → 只捕获 original
    ]

    # 测试小上下文场景 (max_seq_len_to_capture = 4096)
    print("场景 2: max_seq_len_to_capture = 4096 (≤ 8192)")
    test_cases_small = [
        # (batch_size, max_seq_len_to_capture, expected_graph_types)
        (32, 4096, ["original"]),    # 所有情况都只捕获 original
        (64, 4096, ["original"]),    # 所有情况都只捕获 original
        (65, 4096, ["original"]),    # 所有情况都只捕获 original
        (128, 4096, ["original"]),   # 所有情况都只捕获 original
    ]

    all_passed = True

    # 测试大上下文场景
    for batch_size, max_seq_len, expected_types in test_cases_large:
        result = _get_graphs_to_capture_for_batch_size(batch_size, max_seq_len_to_capture=max_seq_len)
        result_types = [graph_type for graph_type, _ in result]

        status = "✅ PASS" if result_types == expected_types else "❌ FAIL"
        print(f"BS={batch_size:3d}, MaxSeq={max_seq_len:5d} → {result_types} (expected: {expected_types}) {status}")

        if result_types != expected_types:
            all_passed = False

    print()

    # 测试小上下文场景
    for batch_size, max_seq_len, expected_types in test_cases_small:
        result = _get_graphs_to_capture_for_batch_size(batch_size, max_seq_len_to_capture=max_seq_len)
        result_types = [graph_type for graph_type, _ in result]

        status = "✅ PASS" if result_types == expected_types else "❌ FAIL"
        print(f"BS={batch_size:3d}, MaxSeq={max_seq_len:5d} → {result_types} (expected: {expected_types}) {status}")

        if result_types != expected_types:
            all_passed = False

    print("=" * 80)
    if all_passed:
        print("✅ 所有图捕获测试用例通过！")
    else:
        print("❌ 部分图捕获测试用例失败！")

    return all_passed

if __name__ == "__main__":
    print("开始测试图类型修改...")
    print()
    
    test1_passed = test_graph_type_logic()
    test2_passed = test_graph_capture_logic()
    
    print("\n" + "=" * 60)
    if test1_passed and test2_passed:
        print("🎉 所有测试通过！修改成功完成。")
        print("\n修改总结:")
        print("- 移除了 'long' 图类型")
        print("- 保留了 'short' 和 'original' 两种图类型")
        print("- 边界值 max_decode_seq_len = 8192 使用 'original' 类型")
        print("- BS > 64 或 SeqLen >= 8192 使用 'original'")
        print("- BS <= 64 且 SeqLen < 8192 使用 'short'")
    else:
        print("❌ 测试失败！需要检查修改。")
        sys.exit(1)

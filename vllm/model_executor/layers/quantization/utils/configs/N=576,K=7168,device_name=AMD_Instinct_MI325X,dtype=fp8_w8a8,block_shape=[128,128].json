{"1": {"BLOCK_SIZE_K": 128, "BLOCK_SIZE_M": 64, "BLOCK_SIZE_N": 32, "GROUP_SIZE_M": 8, "kpack": 1, "matrix_instr_nonkdim": 16, "num_warps": 4}, "2": {"BLOCK_SIZE_K": 128, "BLOCK_SIZE_M": 64, "BLOCK_SIZE_N": 32, "GROUP_SIZE_M": 1, "kpack": 1, "matrix_instr_nonkdim": 16, "num_warps": 4}, "4": {"BLOCK_SIZE_K": 128, "BLOCK_SIZE_M": 64, "BLOCK_SIZE_N": 32, "GROUP_SIZE_M": 32, "kpack": 1, "matrix_instr_nonkdim": 16, "num_warps": 4}, "8": {"BLOCK_SIZE_K": 128, "BLOCK_SIZE_M": 64, "BLOCK_SIZE_N": 32, "GROUP_SIZE_M": 1, "kpack": 1, "matrix_instr_nonkdim": 16, "num_warps": 4}, "16": {"BLOCK_SIZE_K": 128, "BLOCK_SIZE_M": 32, "BLOCK_SIZE_N": 32, "GROUP_SIZE_M": 8, "kpack": 1, "matrix_instr_nonkdim": 16, "num_warps": 4}, "24": {"BLOCK_SIZE_K": 128, "BLOCK_SIZE_M": 64, "BLOCK_SIZE_N": 32, "GROUP_SIZE_M": 16, "kpack": 1, "matrix_instr_nonkdim": 16, "num_warps": 4}, "32": {"BLOCK_SIZE_K": 128, "BLOCK_SIZE_M": 32, "BLOCK_SIZE_N": 32, "GROUP_SIZE_M": 8, "kpack": 1, "matrix_instr_nonkdim": 16, "num_warps": 4}, "48": {"BLOCK_SIZE_K": 128, "BLOCK_SIZE_M": 32, "BLOCK_SIZE_N": 32, "GROUP_SIZE_M": 8, "kpack": 1, "matrix_instr_nonkdim": 16, "num_warps": 4}, "64": {"BLOCK_SIZE_K": 128, "BLOCK_SIZE_M": 32, "BLOCK_SIZE_N": 32, "GROUP_SIZE_M": 16, "kpack": 1, "matrix_instr_nonkdim": 16, "num_warps": 4}, "96": {"BLOCK_SIZE_K": 128, "BLOCK_SIZE_M": 32, "BLOCK_SIZE_N": 32, "GROUP_SIZE_M": 16, "kpack": 1, "matrix_instr_nonkdim": 16, "num_warps": 4}, "128": {"BLOCK_SIZE_K": 128, "BLOCK_SIZE_M": 32, "BLOCK_SIZE_N": 32, "GROUP_SIZE_M": 8, "kpack": 1, "matrix_instr_nonkdim": 16, "num_warps": 4}, "256": {"BLOCK_SIZE_K": 128, "BLOCK_SIZE_M": 32, "BLOCK_SIZE_N": 32, "GROUP_SIZE_M": 8, "kpack": 1, "matrix_instr_nonkdim": 16, "num_warps": 4}, "512": {"BLOCK_SIZE_K": 128, "BLOCK_SIZE_M": 32, "BLOCK_SIZE_N": 32, "GROUP_SIZE_M": 8, "kpack": 1, "matrix_instr_nonkdim": 16, "num_warps": 4}, "1024": {"BLOCK_SIZE_K": 128, "BLOCK_SIZE_M": 32, "BLOCK_SIZE_N": 32, "GROUP_SIZE_M": 1, "kpack": 1, "matrix_instr_nonkdim": 16, "num_warps": 4}, "1536": {"BLOCK_SIZE_K": 128, "BLOCK_SIZE_M": 32, "BLOCK_SIZE_N": 32, "GROUP_SIZE_M": 16, "kpack": 1, "matrix_instr_nonkdim": 16, "num_warps": 4}, "2048": {"BLOCK_SIZE_K": 128, "BLOCK_SIZE_M": 64, "BLOCK_SIZE_N": 32, "GROUP_SIZE_M": 16, "kpack": 1, "matrix_instr_nonkdim": 16, "num_warps": 4}, "3072": {"BLOCK_SIZE_K": 128, "BLOCK_SIZE_M": 32, "BLOCK_SIZE_N": 64, "GROUP_SIZE_M": 32, "kpack": 1, "matrix_instr_nonkdim": 16, "num_warps": 4}, "4096": {"BLOCK_SIZE_K": 128, "BLOCK_SIZE_M": 64, "BLOCK_SIZE_N": 32, "GROUP_SIZE_M": 32, "kpack": 1, "matrix_instr_nonkdim": 16, "num_warps": 4}}
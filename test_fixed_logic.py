#!/usr/bin/env python3
"""
测试修复后的逻辑：当 max_seq_len_to_capture <= 8192 时，应该只使用 original 图
"""

def test_fixed_logic():
    """测试修复后的图类型选择逻辑"""
    
    def _get_graph_type_for_execution(batch_size: int, max_decode_seq_len: int, 
                                    max_seq_len_to_capture: int, short_seq_len_to_capture: int = 8192) -> str:
        """
        修复后的图类型选择逻辑
        """
        # If max_seq_len_to_capture <= short_seq_len_to_capture, always use original (no need for short graph)
        if max_seq_len_to_capture <= short_seq_len_to_capture:
            return "original"
        
        if batch_size > 64 or max_decode_seq_len >= short_seq_len_to_capture:
            return "original"
        else:
            return "short"
    
    def _get_graphs_to_capture_for_batch_size(batch_size: int, 
                                            max_seq_len_to_capture: int, 
                                            short_seq_len_to_capture: int = 8192) -> list:
        """
        修复后的图捕获逻辑
        """
        # If max_seq_len_to_capture <= short_seq_len_to_capture, only capture original graph for all batch sizes
        if max_seq_len_to_capture <= short_seq_len_to_capture:
            return [("original", max_seq_len_to_capture)]
        
        if batch_size > 64:
            # For BS > 64, only capture original graph
            return [("original", max_seq_len_to_capture)]
        else:
            # For BS <= 64 and max_seq_len > short_seq_len_to_capture, capture both short and original graphs
            graphs = [("short", short_seq_len_to_capture)]
            graphs.append(("original", max_seq_len_to_capture))
            return graphs
    
    print("测试修复后的逻辑:")
    print("=" * 80)
    
    # 测试场景 1: max_seq_len_to_capture = 4096 (≤ 8192) - 应该只使用 original
    print("场景 1: max_seq_len_to_capture = 4096 (≤ 8192) - 应该只使用 original")
    test_cases_small = [
        # (batch_size, max_decode_seq_len, max_seq_len_to_capture, expected_graph_type)
        (32, 2048, 4096, "original"),    # 应该是 original，不是 short
        (64, 3999, 4096, "original"),    # 应该是 original，不是 short
        (65, 2048, 4096, "original"),    # 应该是 original
        (128, 4096, 4096, "original"),   # 应该是 original
    ]
    
    all_passed = True
    for batch_size, max_decode_seq_len, max_seq_len_to_capture, expected in test_cases_small:
        result = _get_graph_type_for_execution(batch_size, max_decode_seq_len, max_seq_len_to_capture)
        status = "✅ PASS" if result == expected else "❌ FAIL"
        print(f"BS={batch_size:3d}, SeqLen={max_decode_seq_len:5d}, MaxSeq={max_seq_len_to_capture:5d} → {result:8s} (expected: {expected:8s}) {status}")
        
        if result != expected:
            all_passed = False
    
    print()
    
    # 测试图捕获逻辑
    print("测试图捕获逻辑:")
    capture_test_cases = [
        # (batch_size, max_seq_len_to_capture, expected_graph_types)
        (32, 4096, ["original"]),    # max_seq_len ≤ 8192，只捕获 original
        (64, 4096, ["original"]),    # max_seq_len ≤ 8192，只捕获 original
        (65, 4096, ["original"]),    # max_seq_len ≤ 8192，只捕获 original
        (128, 4096, ["original"]),   # max_seq_len ≤ 8192，只捕获 original
        (32, 16384, ["short", "original"]),  # max_seq_len > 8192，BS ≤ 64，捕获两种
        (65, 16384, ["original"]),   # max_seq_len > 8192，BS > 64，只捕获 original
    ]
    
    for batch_size, max_seq_len, expected_types in capture_test_cases:
        result = _get_graphs_to_capture_for_batch_size(batch_size, max_seq_len)
        result_types = [graph_type for graph_type, _ in result]
        
        status = "✅ PASS" if result_types == expected_types else "❌ FAIL"
        print(f"BS={batch_size:3d}, MaxSeq={max_seq_len:5d} → {result_types} (expected: {expected_types}) {status}")
        
        if result_types != expected_types:
            all_passed = False
    
    print("=" * 80)
    if all_passed:
        print("✅ 修复成功！所有测试用例通过。")
        print("\n修复总结:")
        print("- 当 max_seq_len_to_capture ≤ 8192 时，所有情况都使用 'original' 图")
        print("- 当 max_seq_len_to_capture > 8192 时：")
        print("  - BS > 64 或 SeqLen ≥ 8192 → 'original'")
        print("  - BS ≤ 64 且 SeqLen < 8192 → 'short'")
        print("- 图捕获逻辑也相应调整，避免不必要的 short 图捕获")
    else:
        print("❌ 修复失败！需要进一步检查。")
    
    return all_passed

if __name__ == "__main__":
    test_fixed_logic()
